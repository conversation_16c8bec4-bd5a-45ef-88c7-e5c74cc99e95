# 115网盘复制功能部署指南

## 概述

本指南将帮助你部署115网盘复制功能，实现将Emby/Jellyfin的媒体请求首先复制到另一个115账号下再重定向到115网盘直链，从而减少主账号的服务器流量。

## 前置条件

1. **两个115网盘账号**
   - 主账号：存储原始媒体文件
   - 目标账号：用于复制文件，承担播放流量

2. **alist环境**
   - 主alist实例：连接主115账号
   - 目标alist实例：连接目标115账号（可以是同一服务器的不同端口）

3. **nginx-emby环境**
   - 已部署的emby2alist项目
   - 支持JavaScript的nginx

## 部署步骤

### 第一步：准备115账号

1. **获取主账号cookie**
   ```bash
   # 在浏览器中登录主115账号
   # 打开开发者工具 -> Network -> 找到115.com的请求
   # 复制Cookie头的值
   ```

2. **获取目标账号cookie**
   ```bash
   # 在浏览器中登录目标115账号
   # 同样方式获取Cookie值
   ```

### 第二步：配置alist实例

1. **主alist配置（端口5244）**
   ```yaml
   # docker-compose.yml
   services:
     alist-main:
       image: xhofe/alist:latest
       container_name: alist-main
       restart: unless-stopped
       ports:
         - "5244:5244"
       volumes:
         - ./alist-main:/opt/alist/data
   ```

2. **目标alist配置（端口5245）**
   ```yaml
   # docker-compose.yml
   services:
     alist-target:
       image: xhofe/alist:latest
       container_name: alist-target
       restart: unless-stopped
       ports:
         - "5245:5244"  # 映射到宿主机5245端口
       volumes:
         - ./alist-target:/opt/alist/data
   ```

3. **启动alist实例**
   ```bash
   docker-compose up -d alist-main alist-target
   ```

4. **配置115驱动**
   ```bash
   # 在主alist中添加主115账号
   # 在目标alist中添加目标115账号
   # 确保两个alist都能正常访问各自的115账号
   ```

### 第三步：配置nginx-emby

1. **修改配置文件**
   
   编辑 `conf.d/config/constant-ext.js`：
   ```javascript
   const copy115Config = {
     enable: true, // 启用功能
     
     targetAccount: {
       cookie: "你的目标115账号cookie",
       alistAddr: "http://**********:5245",
       alistToken: "目标alist的token",
     },
     
     copyRule: [
       [2, strHead["115"]], // 复制所有115文件
     ],
     
     cacheExpireMinutes: 60,
     copyTimeoutSeconds: 30,
   };
   ```

2. **获取alist token**
   ```bash
   # 访问目标alist管理界面
   # 设置 -> 其他 -> Token
   # 复制token值
   ```

### 第四步：测试配置

1. **运行测试脚本**
   ```bash
   # 进入nginx容器
   docker exec -it nginx-emby bash
   
   # 运行测试（如果支持）
   cd /etc/nginx/conf.d/test
   node copy115-test.js
   ```

2. **检查配置**
   ```bash
   # 验证目标alist连接
   curl -X POST http://**********:5245/api/fs/list \
     -H "Authorization: your_token" \
     -H "Content-Type: application/json" \
     -d '{"path":"/"}'
   ```

### 第五步：重启服务

```bash
# 重启nginx-emby容器
docker restart nginx-emby

# 查看启动日志
docker logs -f nginx-emby
```

## 配置验证

### 1. 检查日志

```bash
# 查看复制相关日志
docker logs -f nginx-emby 2>&1 | grep copy115

# 预期看到的日志：
# copy115: attempting to copy file before redirect
# copy115: using cached copy for /path/to/file.mkv
# copy115: file already exists in target account
```

### 2. 测试播放

1. 在Emby/Jellyfin中播放一个115网盘的视频文件
2. 观察日志输出，确认复制逻辑被触发
3. 检查是否使用了目标账号的链接

### 3. 验证缓存

1. 播放同一文件两次
2. 第二次应该使用缓存，不会重复复制

## 高级配置

### 1. 自定义复制规则

```javascript
copyRule: [
  // 只复制大文件（需要alist返回文件大小）
  ["alistRes", 2, "size"],
  
  // 只复制特定目录
  ["filePath", 0, "/mnt/115/movies"],
  
  // 只复制视频文件
  [1, [".mkv", ".mp4", ".avi", ".ts"]],
  
  // 使用正则表达式
  [3, /\.(mkv|mp4|avi)$/i],
]
```

### 2. 性能优化

```javascript
const copy115Config = {
  // 增加缓存时间减少重复检查
  cacheExpireMinutes: 120,
  
  // 减少超时时间提高响应速度
  copyTimeoutSeconds: 15,
  
  // 添加更精确的规则减少不必要的处理
  copyRule: [
    ["filePath", 0, "/mnt/115"], // 只处理115挂载路径
    [1, [".mkv", ".mp4"]], // 只处理主要视频格式
  ],
};
```

## 故障排除

### 1. 复制功能不工作

**检查项目：**
- [ ] copy115Config.enable 是否为 true
- [ ] 目标alist地址和token是否正确
- [ ] 复制规则是否匹配文件
- [ ] 网络连接是否正常

**解决方法：**
```bash
# 检查目标alist连接
curl http://**********:5245/ping

# 检查token有效性
curl -H "Authorization: your_token" http://**********:5245/api/me
```

### 2. 性能问题

**症状：**
- 播放启动缓慢
- 频繁的复制操作

**解决方法：**
- 增加缓存时间
- 优化复制规则
- 检查网络延迟

### 3. 缓存问题

**清理缓存：**
```bash
# 重启nginx容器清理内存缓存
docker restart nginx-emby
```

## 监控和维护

### 1. 日志监控

```bash
# 实时监控复制日志
docker logs -f nginx-emby 2>&1 | grep -E "(copy115|115)"

# 统计复制成功率
docker logs nginx-emby 2>&1 | grep "copy115:" | grep -c "completed"
```

### 2. 存储监控

- 定期检查目标115账号存储使用情况
- 监控两个alist实例的运行状态
- 清理不需要的缓存文件

### 3. 性能监控

- 监控复制操作的响应时间
- 统计缓存命中率
- 观察服务器资源使用情况

## 安全注意事项

1. **Cookie安全**
   - 定期更新115账号cookie
   - 不要在日志中输出完整cookie
   - 使用环境变量存储敏感信息

2. **网络安全**
   - 确保alist实例只在内网访问
   - 使用强密码保护alist管理界面
   - 定期更新alist版本

3. **访问控制**
   - 限制对配置文件的访问权限
   - 监控异常的复制请求
   - 设置合理的超时和重试机制

## 扩展功能

### 1. 批量复制

未来可以扩展支持批量复制功能，一次性复制多个文件。

### 2. 智能复制

根据文件访问频率和大小智能决定是否复制。

### 3. 多账号支持

支持配置多个目标账号，实现负载均衡。

## 总结

通过以上步骤，你应该能够成功部署115网盘复制功能。这个功能可以有效减少主115账号的流量消耗，提高媒体服务的稳定性。

如果遇到问题，请检查日志输出并参考故障排除部分。对于复杂的部署环境，可能需要根据实际情况调整配置参数。
