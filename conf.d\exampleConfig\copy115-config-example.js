// 115网盘复制功能配置示例
// 此功能用于将Emby/Jellyfin的媒体请求首先复制到另一个115账号下再重定向到115网盘直链，减少服务器流量

// 在 constant-ext.js 中的配置示例：

const copy115Config = {
  enable: true, // 启用115复制功能
  
  // 目标115账号配置
  targetAccount: {
    // 目标115账号的cookie，可以从浏览器开发者工具中获取
    cookie: "your_target_115_cookie_here",
    
    // 目标115账号对应的alist地址
    alistAddr: "http://172.17.0.1:5245", // 注意端口可能不同
    
    // 目标115账号对应的alist token
    alistToken: "target_alist_token_here",
  },
  
  // 复制规则，匹配的文件将被复制到目标账号
  copyRule: [
    // 示例1: 复制所有115网盘的文件
    [2, "115.com"],
    
    // 示例2: 复制特定路径下的文件
    // ["filePath", 0, "/mnt/115"],
    
    // 示例3: 复制特定文件类型
    // [1, [".mkv", ".mp4", ".avi"]],
    
    // 示例4: 使用正则表达式匹配
    // [3, /\.(mkv|mp4|avi)$/i],
    
    // 示例5: 复制大于某个大小的文件（需要在alist响应中包含文件大小信息）
    // ["alistRes", 2, "size"],
  ],
  
  // 复制缓存时间(分钟)，避免重复复制同一文件
  cacheExpireMinutes: 60,
  
  // 复制超时时间(秒)
  copyTimeoutSeconds: 30,
};

// 使用说明：
// 1. 首先需要准备两个115账号：
//    - 源账号：存储原始文件的账号
//    - 目标账号：用于复制文件的账号，减少源账号流量消耗
//
// 2. 在alist中配置两个115网盘驱动：
//    - 源115账号配置在主alist实例中
//    - 目标115账号配置在另一个alist实例中（可以是同一台服务器的不同端口）
//
// 3. 配置copy115Config：
//    - enable: 设置为true启用功能
//    - targetAccount: 配置目标账号的alist信息
//    - copyRule: 配置哪些文件需要复制
//
// 4. 工作流程：
//    - 当Emby/Jellyfin请求媒体文件时
//    - 系统首先从源alist获取115直链
//    - 检查是否匹配复制规则
//    - 如果匹配，尝试在目标账号中查找相同文件
//    - 如果目标账号中不存在，触发复制操作（需要115 API支持）
//    - 返回目标账号的直链，减少源账号流量
//
// 5. 注意事项：
//    - 目标115账号需要有足够的存储空间
//    - 复制操作可能需要时间，建议设置合理的超时时间
//    - 建议使用缓存避免重复复制相同文件
//    - 当前版本的复制功能需要115官方API支持，如果API不可用，将回退到原始链接

// 复制规则说明：
// 规则格式：[匹配类型, 匹配目标] 或 [源类型, 匹配类型, 匹配目标]
//
// 源类型（可选）：
// - "filePath": 匹配文件路径
// - "alistRes": 匹配alist返回的链接（默认）
//
// 匹配类型：
// - 0: startsWith(str) - 以指定字符串开头
// - 1: endsWith(str) - 以指定字符串结尾
// - 2: includes(str) - 包含指定字符串
// - 3: match(regex) - 正则表达式匹配
//
// 匹配目标：
// - 字符串或字符串数组（对于includes匹配）
// - 正则表达式（对于regex匹配）

export default copy115Config;
