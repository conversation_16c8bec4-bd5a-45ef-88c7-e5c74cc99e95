server_name default;
## SSL SETTINGS ##
listen [::]:8095 ssl; ## Listens on port IPv6 with ssl enabled
listen 8095 ssl; ## Listens on port IPv4 with ssl enabled
# listen 8095 quic reuseport; ## http3 enabled
http2 on; ## since nginx 1.25.1, the "listen ... http" directive is deprecated
add_header Alt-Svc 'h3=":$server_port"; ma=86400'; ## http3 enabled
ssl_session_timeout 30m;
ssl_protocols TLSv1.3 TLSv1.2 TLSv1.1 TLSv1;
ssl_certificate      /etc/nginx/conf.d/cert/fullchain.pem;  ## Location of your public PEM file.
ssl_certificate_key  /etc/nginx/conf.d/cert/privkey.key;  ## Location of your private PEM file.
ssl_session_cache shared:SSL:10m;
error_page 497 =307 https://$host:$server_port$request_uri; ## if http and https use same port, Redirects http:// to https://
