// 115网盘复制功能测试脚本
// 用于验证配置和基本功能

// 模拟nginx环境
const mockNgx = {
  log: (level, message) => console.log(`[${level}] ${message}`),
  WARN: 'WARN',
  ERR: 'ERR',
  fetch: async (url, options) => {
    console.log(`Mock fetch: ${url}`);
    return {
      ok: true,
      json: async () => ({ message: "success", data: { raw_url: "http://mock.115.com/file" } }),
      text: async () => "mock response"
    };
  }
};

global.ngx = mockNgx;

// 模拟配置
const mockConfig = {
  copy115Config: {
    enable: true,
    targetAccount: {
      cookie: "mock_cookie",
      alistAddr: "http://172.17.0.1:5245",
      alistToken: "mock_token",
    },
    copyRule: [
      [2, "115.com"],
    ],
    cacheExpireMinutes: 60,
    copyTimeoutSeconds: 30,
  },
  strHead: {
    "115": "115.com"
  }
};

// 模拟请求对象
const mockRequest = {
  warn: (msg) => console.log(`[WARN] ${msg}`),
  error: (msg) => console.log(`[ERROR] ${msg}`),
  log: (msg) => console.log(`[LOG] ${msg}`),
  headersIn: {
    "User-Agent": "Test-Agent"
  }
};

// 测试函数
async function testCopy115Config() {
  console.log('=== 115网盘复制功能配置测试 ===\n');

  // 1. 测试配置加载
  console.log('1. 测试配置加载...');
  if (mockConfig.copy115Config.enable) {
    console.log('✓ 复制功能已启用');
  } else {
    console.log('✗ 复制功能未启用');
    return;
  }

  // 2. 测试目标账号配置
  console.log('\n2. 测试目标账号配置...');
  const targetConfig = mockConfig.copy115Config.targetAccount;
  if (targetConfig.alistAddr && targetConfig.alistToken) {
    console.log('✓ 目标账号配置完整');
    console.log(`  - alist地址: ${targetConfig.alistAddr}`);
    console.log(`  - token: ${targetConfig.alistToken.substring(0, 10)}...`);
  } else {
    console.log('✗ 目标账号配置不完整');
  }

  // 3. 测试复制规则
  console.log('\n3. 测试复制规则...');
  const copyRules = mockConfig.copy115Config.copyRule;
  if (copyRules && copyRules.length > 0) {
    console.log(`✓ 配置了 ${copyRules.length} 条复制规则`);
    copyRules.forEach((rule, index) => {
      console.log(`  规则${index + 1}: ${JSON.stringify(rule)}`);
    });
  } else {
    console.log('✗ 未配置复制规则');
  }

  // 4. 测试规则匹配
  console.log('\n4. 测试规则匹配...');
  const testUrls = [
    "https://alist.example.com/d/test.mkv?sign=xxx&115.com",
    "https://other.example.com/d/test.mp4",
    "https://115.com/download/test.avi"
  ];

  testUrls.forEach((url, index) => {
    const matches = copyRules.some(rule => {
      if (rule[0] === 2) { // includes
        return url.includes(rule[1]);
      }
      return false;
    });
    console.log(`  URL${index + 1}: ${matches ? '✓' : '✗'} ${url}`);
  });

  // 5. 测试缓存配置
  console.log('\n5. 测试缓存配置...');
  const cacheMinutes = mockConfig.copy115Config.cacheExpireMinutes;
  const timeoutSeconds = mockConfig.copy115Config.copyTimeoutSeconds;
  console.log(`✓ 缓存过期时间: ${cacheMinutes} 分钟`);
  console.log(`✓ 复制超时时间: ${timeoutSeconds} 秒`);

  console.log('\n=== 测试完成 ===');
}

// 模拟copy115Api类的基本功能测试
function testCopy115Api() {
  console.log('\n=== Copy115Api 功能测试 ===\n');

  // 模拟Copy115Api类
  class MockCopy115Api {
    constructor() {
      this.copyCache = new Map();
    }

    shouldCopy(r, filePath, alistRes) {
      if (!mockConfig.copy115Config.enable) {
        return false;
      }

      if (!alistRes || !alistRes.includes(mockConfig.strHead["115"])) {
        return false;
      }

      const copyRules = mockConfig.copy115Config.copyRule;
      if (!copyRules || copyRules.length === 0) {
        return true;
      }

      for (const rule of copyRules) {
        if (this.matchRule(r, rule, filePath, alistRes)) {
          return true;
        }
      }

      return false;
    }

    matchRule(r, rule, filePath, alistRes) {
      if (!rule || rule.length < 2) {
        return false;
      }

      const [matchType, matchTarget] = rule;
      let sourceValue = alistRes;

      if (typeof matchType === 'string') {
        if (matchType === 'filePath') {
          sourceValue = filePath;
        }
        return this.applyMatch(rule.slice(1), sourceValue);
      }

      return this.applyMatch(rule, sourceValue);
    }

    applyMatch(rule, sourceValue) {
      if (rule.length < 2) {
        return false;
      }

      const [matchType, matchTarget] = rule;
      
      switch (matchType) {
        case 0: // startsWith
          return sourceValue.startsWith(matchTarget);
        case 1: // endsWith
          return sourceValue.endsWith(matchTarget);
        case 2: // includes
          if (Array.isArray(matchTarget)) {
            return matchTarget.some(target => sourceValue.includes(target));
          }
          return sourceValue.includes(matchTarget);
        case 3: // regex match
          if (matchTarget instanceof RegExp) {
            return matchTarget.test(sourceValue);
          }
          return false;
        default:
          return false;
      }
    }

    getCacheKey(filePath) {
      // 简化的hash函数
      return `copy115_${filePath.replace(/[^a-zA-Z0-9]/g, '_')}`;
    }

    getCachedCopy(filePath) {
      const cacheKey = this.getCacheKey(filePath);
      const cached = this.copyCache.get(cacheKey);
      
      if (!cached) {
        return null;
      }

      const now = Date.now();
      const expireTime = cached.timestamp + (mockConfig.copy115Config.cacheExpireMinutes * 60 * 1000);
      
      if (now > expireTime) {
        this.copyCache.delete(cacheKey);
        return null;
      }

      return cached.targetUrl;
    }

    setCachedCopy(filePath, targetUrl) {
      const cacheKey = this.getCacheKey(filePath);
      this.copyCache.set(cacheKey, {
        targetUrl: targetUrl,
        timestamp: Date.now()
      });
    }
  }

  const api = new MockCopy115Api();

  // 测试shouldCopy方法
  console.log('1. 测试shouldCopy方法...');
  const testCases = [
    {
      filePath: "/mnt/115/movie.mkv",
      alistRes: "https://alist.com/d/movie.mkv?115.com",
      expected: true
    },
    {
      filePath: "/mnt/local/movie.mkv", 
      alistRes: "https://alist.com/d/movie.mkv",
      expected: false
    },
    {
      filePath: "/mnt/115/movie.mp4",
      alistRes: "https://115.com/download/movie.mp4",
      expected: true
    }
  ];

  testCases.forEach((testCase, index) => {
    const result = api.shouldCopy(mockRequest, testCase.filePath, testCase.alistRes);
    const status = result === testCase.expected ? '✓' : '✗';
    console.log(`  测试${index + 1}: ${status} ${testCase.filePath} -> ${result}`);
  });

  // 测试缓存功能
  console.log('\n2. 测试缓存功能...');
  const testFilePath = "/mnt/115/test.mkv";
  const testUrl = "https://target.115.com/test.mkv";

  // 设置缓存
  api.setCachedCopy(testFilePath, testUrl);
  console.log('✓ 设置缓存成功');

  // 获取缓存
  const cachedUrl = api.getCachedCopy(testFilePath);
  if (cachedUrl === testUrl) {
    console.log('✓ 获取缓存成功');
  } else {
    console.log('✗ 获取缓存失败');
  }

  // 测试缓存过期（模拟）
  console.log('✓ 缓存功能测试完成');

  console.log('\n=== Copy115Api 测试完成 ===');
}

// 运行测试
async function runTests() {
  await testCopy115Config();
  testCopy115Api();
  
  console.log('\n=== 使用说明 ===');
  console.log('1. 确保在 constant-ext.js 中正确配置 copy115Config');
  console.log('2. 配置目标115账号的alist实例');
  console.log('3. 根据需要调整复制规则');
  console.log('4. 重启nginx容器使配置生效');
  console.log('5. 查看日志确认功能正常工作');
}

// 如果直接运行此脚本
if (typeof module !== 'undefined' && require.main === module) {
  runTests().catch(console.error);
}

export { testCopy115Config, testCopy115Api, runTests };
