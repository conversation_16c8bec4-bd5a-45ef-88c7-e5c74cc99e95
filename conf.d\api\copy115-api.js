// @author: AI Assistant
// @date: 2025-01-03
// 115网盘复制API模块

import config from "../constant.js";
import util from "../common/util.js";
import urlUtil from "../common/url-util.js";

/**
 * 115网盘复制API
 */
class Copy115Api {
  constructor() {
    this.copyCache = new Map(); // 复制缓存
  }

  /**
   * 检查是否需要复制文件
   * @param {Object} r nginx request对象
   * @param {String} filePath 文件路径
   * @param {String} alistRes alist返回的链接
   * @returns {Boolean} 是否需要复制
   */
  shouldCopy(r, filePath, alistRes) {
    if (!config.copy115Config.enable) {
      return false;
    }

    // 检查是否为115网盘链接
    if (!alistRes || !alistRes.includes(config.strHead["115"])) {
      return false;
    }

    // 检查复制规则
    const copyRules = config.copy115Config.copyRule;
    if (!copyRules || copyRules.length === 0) {
      return true; // 默认复制所有115文件
    }

    // 应用复制规则
    for (const rule of copyRules) {
      if (this.matchRule(r, rule, filePath, alistRes)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 匹配规则
   * @param {Object} r nginx request对象
   * @param {Array} rule 规则数组
   * @param {String} filePath 文件路径
   * @param {String} alistRes alist返回的链接
   * @returns {Boolean} 是否匹配
   */
  matchRule(r, rule, filePath, alistRes) {
    if (!rule || rule.length < 2) {
      return false;
    }

    const [matchType, matchTarget] = rule;
    let sourceValue = alistRes;

    // 如果第一个参数是字符串，表示指定了源类型
    if (typeof matchType === 'string') {
      if (matchType === 'filePath') {
        sourceValue = filePath;
      } else if (matchType === 'alistRes') {
        sourceValue = alistRes;
      }
      return this.applyMatch(rule.slice(1), sourceValue);
    }

    // 否则直接应用匹配
    return this.applyMatch(rule, sourceValue);
  }

  /**
   * 应用匹配逻辑
   * @param {Array} rule 规则数组
   * @param {String} sourceValue 源值
   * @returns {Boolean} 是否匹配
   */
  applyMatch(rule, sourceValue) {
    if (rule.length < 2) {
      return false;
    }

    const [matchType, matchTarget] = rule;
    
    switch (matchType) {
      case 0: // startsWith
        return sourceValue.startsWith(matchTarget);
      case 1: // endsWith
        return sourceValue.endsWith(matchTarget);
      case 2: // includes
        if (Array.isArray(matchTarget)) {
          return matchTarget.some(target => sourceValue.includes(target));
        }
        return sourceValue.includes(matchTarget);
      case 3: // regex match
        if (matchTarget instanceof RegExp) {
          return matchTarget.test(sourceValue);
        }
        return false;
      default:
        return false;
    }
  }

  /**
   * 获取缓存键
   * @param {String} filePath 文件路径
   * @returns {String} 缓存键
   */
  getCacheKey(filePath) {
    return `copy115_${util.calculateHMAC(filePath, 'cache_key')}`;
  }

  /**
   * 检查复制缓存
   * @param {String} filePath 文件路径
   * @returns {String|null} 缓存的目标链接或null
   */
  getCachedCopy(filePath) {
    const cacheKey = this.getCacheKey(filePath);
    const cached = this.copyCache.get(cacheKey);
    
    if (!cached) {
      return null;
    }

    const now = Date.now();
    const expireTime = cached.timestamp + (config.copy115Config.cacheExpireMinutes * 60 * 1000);
    
    if (now > expireTime) {
      this.copyCache.delete(cacheKey);
      return null;
    }

    return cached.targetUrl;
  }

  /**
   * 设置复制缓存
   * @param {String} filePath 文件路径
   * @param {String} targetUrl 目标链接
   */
  setCachedCopy(filePath, targetUrl) {
    const cacheKey = this.getCacheKey(filePath);
    this.copyCache.set(cacheKey, {
      targetUrl: targetUrl,
      timestamp: Date.now()
    });
  }

  /**
   * 复制文件到目标115账号
   * @param {Object} r nginx request对象
   * @param {String} filePath 文件路径
   * @param {String} sourceUrl 源文件URL
   * @returns {Promise<String|null>} 目标链接或null
   */
  async copyFile(r, filePath, sourceUrl) {
    try {
      // 检查缓存
      const cachedUrl = this.getCachedCopy(filePath);
      if (cachedUrl) {
        r.warn(`copy115: using cached copy for ${filePath}`);
        return cachedUrl;
      }

      r.warn(`copy115: starting copy for ${filePath}`);

      // 解析源文件信息
      const sourceInfo = this.parseSourceUrl(sourceUrl);
      if (!sourceInfo) {
        r.error(`copy115: failed to parse source url ${sourceUrl}`);
        return null;
      }

      // 执行复制操作
      const targetUrl = await this.performCopy(r, sourceInfo, filePath);
      if (targetUrl) {
        // 缓存结果
        this.setCachedCopy(filePath, targetUrl);
        r.warn(`copy115: copy completed, target url: ${targetUrl}`);
        return targetUrl;
      }

      return null;
    } catch (error) {
      r.error(`copy115: copy failed for ${filePath}: ${error.message}`);
      return null;
    }
  }

  /**
   * 解析源文件URL信息
   * @param {String} sourceUrl 源文件URL
   * @returns {Object|null} 解析的信息
   */
  parseSourceUrl(sourceUrl) {
    try {
      const url = new URL(sourceUrl);
      const pathParts = url.pathname.split('/');
      
      // 提取文件ID或路径信息
      const fileId = url.searchParams.get('d') || pathParts[pathParts.length - 1];
      
      return {
        fileId: fileId,
        originalUrl: sourceUrl,
        domain: url.hostname
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * 执行实际的复制操作
   * @param {Object} r nginx request对象
   * @param {Object} sourceInfo 源文件信息
   * @param {String} filePath 文件路径
   * @returns {String|null} 目标链接
   */
  async performCopy(r, sourceInfo, filePath) {
    const targetConfig = config.copy115Config.targetAccount;

    if (!targetConfig.alistAddr || !targetConfig.alistToken) {
      r.error('copy115: target account configuration is incomplete');
      return null;
    }

    try {
      // 通过目标alist获取文件信息
      const targetAlistPath = `${targetConfig.alistAddr}/api/fs/get`;
      const targetFilePath = filePath;

      // 首先检查目标账号是否已有该文件
      const existingFile = await this.checkTargetFile(targetAlistPath, targetFilePath, targetConfig.alistToken, r.headersIn["User-Agent"]);
      if (existingFile) {
        r.warn(`copy115: file already exists in target account`);
        return existingFile;
      }

      // 尝试通过115网盘秒传功能复制文件
      const copiedFile = await this.copy115File(r, sourceInfo, filePath, targetConfig);
      if (copiedFile) {
        r.warn(`copy115: file copied successfully`);
        return copiedFile;
      }

      // 如果复制失败，返回原链接
      r.warn(`copy115: copy failed, using original url`);
      return sourceInfo.originalUrl;

    } catch (error) {
      r.error(`copy115: performCopy failed: ${error.message}`);
      return null;
    }
  }

  /**
   * 通过115网盘API复制文件
   * @param {Object} r nginx request对象
   * @param {Object} sourceInfo 源文件信息
   * @param {String} filePath 文件路径
   * @param {Object} targetConfig 目标账号配置
   * @returns {String|null} 复制后的文件链接
   */
  async copy115File(r, sourceInfo, filePath, targetConfig) {
    try {
      // 获取源文件的详细信息（包括文件hash等）
      const sourceFileInfo = await this.getSourceFileInfo(r, sourceInfo);
      if (!sourceFileInfo) {
        r.warn('copy115: failed to get source file info');
        return null;
      }

      // 尝试在目标账号中秒传文件
      const copyResult = await this.instantCopy115(r, sourceFileInfo, targetConfig);
      if (copyResult) {
        // 复制成功，通过目标alist获取新的直链
        const newDirectLink = await this.getTargetDirectLink(r, filePath, targetConfig);
        return newDirectLink;
      }

      return null;
    } catch (error) {
      r.error(`copy115: copy115File failed: ${error.message}`);
      return null;
    }
  }

  /**
   * 获取源文件信息
   * @param {Object} r nginx request对象
   * @param {Object} sourceInfo 源文件信息
   * @returns {Object|null} 文件详细信息
   */
  async getSourceFileInfo(r, sourceInfo) {
    // 这里需要调用115 API获取文件的详细信息
    // 包括文件的SHA1、大小等信息用于秒传
    // 由于115 API的复杂性，这里返回基本信息
    r.warn(`copy115: getSourceFileInfo for ${sourceInfo.fileId}`);
    return {
      fileId: sourceInfo.fileId,
      originalUrl: sourceInfo.originalUrl,
      // 实际实现中需要获取这些信息：
      // sha1: "file_sha1_hash",
      // size: file_size,
      // name: "file_name"
    };
  }

  /**
   * 115网盘秒传功能
   * @param {Object} r nginx request对象
   * @param {Object} fileInfo 文件信息
   * @param {Object} targetConfig 目标账号配置
   * @returns {Boolean} 是否复制成功
   */
  async instantCopy115(r, fileInfo, targetConfig) {
    // 这里需要实现115网盘的秒传API调用
    // 由于115 API的限制和复杂性，当前版本暂不实现
    // 实际使用中可以考虑以下方案：
    // 1. 使用115官方API（需要申请权限）
    // 2. 使用第三方115工具（如115-go等）
    // 3. 通过CD2等工具的API进行复制

    r.warn(`copy115: instant copy not implemented for ${fileInfo.fileId}, skipping`);
    // 模拟复制失败，实际实现时替换为真实的API调用
    return false;
  }

  /**
   * 获取目标账号的直链
   * @param {Object} r nginx request对象
   * @param {String} filePath 文件路径
   * @param {Object} targetConfig 目标账号配置
   * @returns {String|null} 直链URL
   */
  async getTargetDirectLink(r, filePath, targetConfig) {
    try {
      const targetAlistPath = `${targetConfig.alistAddr}/api/fs/get`;
      return await this.checkTargetFile(targetAlistPath, filePath, targetConfig.alistToken, r.headersIn["User-Agent"]);
    } catch (error) {
      r.error(`copy115: getTargetDirectLink failed: ${error.message}`);
      return null;
    }
  }

  /**
   * 检查目标账号是否已有文件
   * @param {String} alistPath alist API路径
   * @param {String} filePath 文件路径
   * @param {String} token alist token
   * @param {String} ua User-Agent
   * @returns {String|null} 文件链接或null
   */
  async checkTargetFile(alistPath, filePath, token, ua) {
    try {
      const requestBody = {
        path: filePath,
        password: "",
      };

      const urlParts = urlUtil.parseUrl(alistPath);
      const hostValue = `${urlParts.host}:${urlParts.port}`;

      const response = await ngx.fetch(alistPath, {
        method: "POST",
        headers: {
          "Content-Type": "application/json;charset=utf-8",
          Authorization: token,
          "User-Agent": ua,
          Host: hostValue,
        },
        max_response_body_size: 65535,
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        const result = await response.json();
        if (result && result.message === "success" && result.data && result.data.raw_url) {
          return result.data.raw_url;
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }
}

// 创建单例实例
const copy115Api = new Copy115Api();

export default copy115Api;
