# 115网盘复制功能说明

## 功能概述

115网盘复制功能允许在Emby/Jellyfin请求媒体文件时，自动将文件复制到另一个115账号下，然后重定向到新账号的直链，从而减少主账号的流量消耗。

## 工作原理

1. **请求拦截**: 当Emby/Jellyfin请求媒体文件时，系统拦截请求
2. **规则匹配**: 检查文件是否匹配预设的复制规则
3. **缓存检查**: 检查是否已经复制过该文件（避免重复复制）
4. **文件复制**: 如果需要复制且未缓存，则将文件复制到目标115账号
5. **链接重定向**: 返回目标账号的直链给客户端

## 配置方法

### 1. 基础配置

在 `conf.d/config/constant-ext.js` 中配置：

```javascript
const copy115Config = {
  enable: true, // 启用功能
  
  // 目标115账号配置
  targetAccount: {
    cookie: "your_target_115_cookie", // 目标账号cookie
    alistAddr: "http://**********:5245", // 目标alist地址
    alistToken: "target_alist_token", // 目标alist token
  },
  
  // 复制规则
  copyRule: [
    [2, "115.com"], // 复制所有115网盘文件
  ],
  
  cacheExpireMinutes: 60, // 缓存过期时间
  copyTimeoutSeconds: 30, // 复制超时时间
};
```

### 2. 复制规则配置

复制规则支持多种匹配方式：

```javascript
copyRule: [
  // 复制所有115网盘文件
  [2, "115.com"],
  
  // 复制特定路径下的文件
  ["filePath", 0, "/mnt/115"],
  
  // 复制特定文件类型
  [1, [".mkv", ".mp4", ".avi"]],
  
  // 使用正则表达式
  [3, /\.(mkv|mp4|avi)$/i],
]
```

#### 规则格式说明

- `[匹配类型, 匹配目标]`
- `[源类型, 匹配类型, 匹配目标]`

**源类型**:
- `"filePath"`: 匹配文件路径
- `"alistRes"`: 匹配alist返回的链接（默认）

**匹配类型**:
- `0`: startsWith - 以指定字符串开头
- `1`: endsWith - 以指定字符串结尾  
- `2`: includes - 包含指定字符串
- `3`: match - 正则表达式匹配

## 部署步骤

### 1. 准备两个115账号

- **主账号**: 存储原始文件
- **目标账号**: 用于复制文件，承担流量

### 2. 配置alist

为两个115账号分别配置alist驱动：

```bash
# 主alist实例（端口5244）
# 配置主115账号

# 目标alist实例（端口5245）  
# 配置目标115账号
```

### 3. 更新配置文件

修改 `conf.d/config/constant-ext.js`：

```javascript
const copy115Config = {
  enable: true,
  targetAccount: {
    cookie: "从目标115账号获取的cookie",
    alistAddr: "http://**********:5245",
    alistToken: "目标alist的token",
  },
  copyRule: [
    [2, "115.com"], // 根据需要调整规则
  ],
  cacheExpireMinutes: 60,
  copyTimeoutSeconds: 30,
};
```

### 4. 重启nginx

```bash
docker restart nginx-emby
```

## 功能特性

### 1. 智能缓存

- 避免重复复制相同文件
- 可配置缓存过期时间
- 内存缓存，重启后清空

### 2. 规则匹配

- 支持多种匹配方式
- 可配置复制特定类型文件
- 支持正则表达式

### 3. 错误处理

- 复制失败时自动回退到原链接
- 详细的日志记录
- 超时保护

### 4. 性能优化

- 异步复制操作
- 缓存机制减少重复操作
- 可配置超时时间

## 注意事项

### 1. 115 API限制

当前版本的复制功能依赖115官方API，由于API限制：
- 实际复制功能可能需要第三方工具支持
- 建议使用CD2等工具的API接口
- 可考虑使用115-go等开源工具

### 2. 存储空间

- 确保目标115账号有足够存储空间
- 监控两个账号的使用情况
- 定期清理不需要的文件

### 3. 网络要求

- 确保服务器能访问两个alist实例
- 网络延迟会影响复制速度
- 建议在同一网络环境部署

### 4. 安全考虑

- 妥善保管115账号cookie
- 定期更新alist token
- 监控异常访问

## 故障排除

### 1. 复制失败

检查日志中的错误信息：
```bash
docker logs -f nginx-emby 2>&1 | grep copy115
```

常见问题：
- 目标alist配置错误
- 115 cookie过期
- 网络连接问题

### 2. 性能问题

- 调整缓存过期时间
- 优化复制规则
- 检查网络延迟

### 3. 配置问题

- 验证alist连接
- 检查token有效性
- 确认规则语法正确

## 日志示例

```
copy115: attempting to copy file before redirect
copy115: using cached copy for /path/to/file.mkv
copy115: file already exists in target account
copy115: copy completed, target url: https://target.115.com/...
```

## 扩展开发

如需扩展功能，可以修改以下文件：
- `conf.d/api/copy115-api.js` - 核心复制逻辑
- `conf.d/config/constant-ext.js` - 配置选项
- `conf.d/emby.js` - 主要调用逻辑

## 版本历史

- v1.0: 基础复制功能框架
- 待开发: 实际115 API集成
- 待开发: CD2工具集成
- 待开发: 批量复制功能
